#!/usr/bin/env python3
"""
一键启动 Augment 代理
"""

import os
import sys
import subprocess
import threading
import time
import requests
import signal

def check_dependencies():
    """检查依赖"""
    try:
        import requests
        return True
    except ImportError:
        print("❌ 缺少 requests 库")
        print("📦 请安装: pip install requests")
        return False

def test_proxy(proxy_url):
    """测试代理连接"""
    try:
        print(f"🧪 测试代理连接: {proxy_url}")
        
        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }
        
        response = requests.get(
            'http://httpbin.org/ip',
            proxies=proxies,
            timeout=10
        )
        
        if response.status_code == 200:
            ip_info = response.json()
            print(f"✅ 代理测试成功! 当前 IP: {ip_info.get('origin', 'Unknown')}")
            return True
        else:
            print(f"❌ 代理测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 代理测试失败: {e}")
        return False

def start_proxy_server():
    """启动代理服务器"""
    print("🚀 启动代理服务器...")
    
    try:
        # 启动代理服务器进程
        process = subprocess.Popen([
            sys.executable, 'proxy_server.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # 等待服务器启动
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 代理服务器启动成功!")
            return process
        else:
            stdout, stderr = process.communicate()
            print(f"❌ 代理服务器启动失败:")
            print(f"stdout: {stdout}")
            print(f"stderr: {stderr}")
            return None
            
    except Exception as e:
        print(f"❌ 启动代理服务器时出错: {e}")
        return None

def set_environment():
    """设置环境变量"""
    proxy_url = "http://127.0.0.1:8888"
    
    env_vars = {
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url,
        'http_proxy': proxy_url,
        'https_proxy': proxy_url,
    }
    
    print("🔧 设置环境变量...")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"   ✅ {key} = {value}")
    
    return proxy_url

def main():
    """主函数"""
    print("🌟 Augment 代理一键启动工具")
    print("="*50)
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 设置环境变量
    proxy_url = set_environment()
    
    # 启动代理服务器
    proxy_process = start_proxy_server()
    
    if not proxy_process:
        print("❌ 无法启动代理服务器，退出")
        return
    
    try:
        # 测试代理
        time.sleep(2)
        test_success = test_proxy(proxy_url)
        
        if test_success:
            print("\n🎉 代理设置完成!")
            print("📋 现在你可以:")
            print("   1. 在当前终端启动 Augment")
            print("   2. 或者在新终端中设置相同的环境变量后启动 Augment")
            print("\n💡 提示:")
            print("   - 保持此脚本运行以维持代理服务")
            print("   - 按 Ctrl+C 停止代理服务")
            print("   - 所有请求将通过新加坡 IP: *************")
        else:
            print("⚠️  代理测试失败，但服务器已启动")
            print("💡 你可以手动测试或检查网络连接")
        
        print("\n⏳ 代理服务器运行中... (按 Ctrl+C 停止)")
        
        # 等待用户中断
        while True:
            time.sleep(1)
            
            # 检查代理进程是否还在运行
            if proxy_process.poll() is not None:
                print("❌ 代理服务器进程已退出")
                break
    
    except KeyboardInterrupt:
        print("\n⏹️  正在停止代理服务器...")
        
        # 终止代理进程
        if proxy_process and proxy_process.poll() is None:
            proxy_process.terminate()
            try:
                proxy_process.wait(timeout=5)
                print("✅ 代理服务器已停止")
            except subprocess.TimeoutExpired:
                proxy_process.kill()
                print("🔪 强制终止代理服务器")
    
    except Exception as e:
        print(f"❌ 运行时出错: {e}")
    
    finally:
        # 清理环境变量
        env_vars_to_clear = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in env_vars_to_clear:
            if var in os.environ:
                del os.environ[var]
        
        print("🧹 环境变量已清理")
        print("👋 再见!")

if __name__ == "__main__":
    main()
