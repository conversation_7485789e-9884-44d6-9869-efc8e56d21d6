#!/usr/bin/env python3
"""
系统级代理设置 - 强制所有应用使用新加坡代理
"""

import os
import sys
import subprocess
import winreg
import requests
import time

def set_windows_proxy(enable=True, proxy_server="127.0.0.1:7897"):
    """设置 Windows 系统代理"""
    try:
        # 打开注册表
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
            0,
            winreg.KEY_ALL_ACCESS
        )
        
        if enable:
            # 启用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
            winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, proxy_server)
            # 设置代理覆盖（绕过本地地址）
            winreg.SetValueEx(key, "ProxyOverride", 0, winreg.REG_SZ, "localhost;127.*;10.*;172.16.*;172.17.*;172.18.*;172.19.*;172.20.*;172.21.*;172.22.*;172.23.*;172.24.*;172.25.*;172.26.*;172.27.*;172.28.*;172.29.*;172.30.*;172.31.*;192.168.*")
            print(f"[系统] 已设置系统代理: {proxy_server}")
        else:
            # 禁用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            print("[系统] 已禁用系统代理")
        
        winreg.CloseKey(key)
        
        # 刷新系统设置
        subprocess.run([
            "rundll32.exe", 
            "wininet.dll,InternetSetOption", 
            "NULL", "37", "NULL", "0"
        ], check=False, capture_output=True)
        
        return True
        
    except Exception as e:
        print(f"[错误] 设置系统代理失败: {e}")
        return False

def test_ip():
    """测试当前 IP"""
    try:
        response = requests.get('http://httpbin.org/ip', timeout=10)
        if response.status_code == 200:
            ip_info = response.json()
            current_ip = ip_info.get('origin', 'Unknown')
            return current_ip
        return None
    except:
        return None

def main():
    print("系统级代理设置工具")
    print("=" * 40)
    
    # 测试当前 IP
    print("[检查] 当前 IP 状态...")
    current_ip = test_ip()
    if current_ip:
        print(f"[当前] IP: {current_ip}")
        if current_ip.startswith('188.253'):
            print("[状态] 已经在使用新加坡 IP")
        else:
            print("[状态] 当前使用中国 IP，需要设置代理")
    else:
        print("[错误] 无法获取当前 IP")
    
    print("\n[选择] 请选择操作:")
    print("1. 设置系统代理 (直接使用 Clash)")
    print("2. 启动中转代理服务器")
    print("3. 禁用系统代理")
    print("4. 检查当前状态")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        # 直接使用 Clash 代理
        print("\n[设置] 设置系统代理为 Clash...")
        if set_windows_proxy(True, "127.0.0.1:7897"):
            print("[成功] 系统代理已设置")
            print("[说明] 请重启浏览器或应用程序")
            
            # 等待几秒后测试
            print("[等待] 等待 3 秒后测试...")
            time.sleep(3)
            
            new_ip = test_ip()
            if new_ip:
                print(f"[测试] 新 IP: {new_ip}")
                if new_ip.startswith('188.253'):
                    print("[成功] 现在使用新加坡 IP!")
                else:
                    print("[警告] 仍然是中国 IP，可能需要重启应用")
        else:
            print("[失败] 设置系统代理失败")
    
    elif choice == "2":
        print("\n[启动] 启动中转代理服务器...")
        print("[说明] 这将启动一个中转服务器，然后设置系统代理")
        
        # 启动中转代理
        try:
            subprocess.Popen([sys.executable, "smart_proxy.py"])
            time.sleep(2)
            
            # 设置系统代理指向中转服务器
            if set_windows_proxy(True, "127.0.0.1:8888"):
                print("[成功] 中转代理已启动并设置")
                print("[说明] 请重启浏览器或应用程序")
            else:
                print("[失败] 设置系统代理失败")
                
        except Exception as e:
            print(f"[错误] 启动中转代理失败: {e}")
    
    elif choice == "3":
        print("\n[禁用] 禁用系统代理...")
        if set_windows_proxy(False):
            print("[成功] 系统代理已禁用")
        else:
            print("[失败] 禁用系统代理失败")
    
    elif choice == "4":
        print("\n[检查] 当前状态...")
        
        # 检查注册表设置
        try:
            key = winreg.OpenKey(
                winreg.HKEY_CURRENT_USER,
                r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
                0,
                winreg.KEY_READ
            )
            
            proxy_enable = winreg.QueryValueEx(key, "ProxyEnable")[0]
            if proxy_enable:
                proxy_server = winreg.QueryValueEx(key, "ProxyServer")[0]
                print(f"[系统] 系统代理已启用: {proxy_server}")
            else:
                print("[系统] 系统代理已禁用")
            
            winreg.CloseKey(key)
            
        except Exception as e:
            print(f"[错误] 读取系统代理设置失败: {e}")
        
        # 测试当前 IP
        current_ip = test_ip()
        if current_ip:
            print(f"[当前] 当前 IP: {current_ip}")
        
    else:
        print("[错误] 无效选择")
    
    print("\n[提示] 设置完成后，请:")
    print("1. 重启浏览器")
    print("2. 重启 Augment 或其他应用")
    print("3. 检查应用是否使用了新的 IP")

if __name__ == "__main__":
    main()
