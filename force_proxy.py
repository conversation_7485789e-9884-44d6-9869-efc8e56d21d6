#!/usr/bin/env python3
"""
强制代理脚本 - 确保所有流量都通过新加坡代理
"""

import os
import sys
import subprocess
import winreg
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
import time
import json

class ForceProxyHandler(BaseHTTPRequestHandler):
    PROXY_IP = "127.0.0.1"
    PROXY_PORT = 7897
    
    def log_message(self, format, *args):
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")
    
    def do_GET(self):
        self._handle_request()
    
    def do_POST(self):
        self._handle_request()
    
    def do_PUT(self):
        self._handle_request()
    
    def do_DELETE(self):
        self._handle_request()
    
    def do_PATCH(self):
        self._handle_request()
    
    def do_OPTIONS(self):
        self._handle_request()
    
    def _handle_request(self):
        try:
            url = self.path
            method = self.command
            headers = dict(self.headers)
            
            content_length = int(headers.get('Content-Length', 0))
            body = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 清理头部
            for header in ['host', 'connection', 'content-length']:
                headers.pop(header, None)
            
            # 强制使用新加坡代理
            proxies = {
                'http': f'http://{self.PROXY_IP}:{self.PROXY_PORT}',
                'https': f'http://{self.PROXY_IP}:{self.PROXY_PORT}'
            }
            
            print(f"[代理] {method} {url} -> 新加坡")
            
            # 发送请求
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body,
                proxies=proxies,
                timeout=30,
                verify=False
            )
            
            # 返回响应
            self.send_response(response.status_code)
            
            for key, value in response.headers.items():
                if key.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(key, value)
            
            self.end_headers()
            self.wfile.write(response.content)
            
        except Exception as e:
            print(f"[错误] {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(f"代理错误: {e}".encode('utf-8'))

def set_system_proxy(enable=True, proxy_server="127.0.0.1:8888"):
    """设置 Windows 系统代理"""
    try:
        # 打开注册表
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Internet Settings",
            0,
            winreg.KEY_ALL_ACCESS
        )
        
        if enable:
            # 启用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 1)
            winreg.SetValueEx(key, "ProxyServer", 0, winreg.REG_SZ, proxy_server)
            print(f"[系统] 已设置系统代理: {proxy_server}")
        else:
            # 禁用代理
            winreg.SetValueEx(key, "ProxyEnable", 0, winreg.REG_DWORD, 0)
            print("[系统] 已禁用系统代理")
        
        winreg.CloseKey(key)
        
        # 刷新系统设置
        subprocess.run([
            "rundll32.exe", 
            "wininet.dll,InternetSetOption", 
            "NULL", "37", "NULL", "0"
        ], check=False)
        
        return True
        
    except Exception as e:
        print(f"[错误] 设置系统代理失败: {e}")
        return False

def test_current_ip():
    """测试当前 IP"""
    try:
        response = requests.get('http://httpbin.org/ip', timeout=10)
        if response.status_code == 200:
            ip_info = response.json()
            current_ip = ip_info.get('origin', 'Unknown')
            print(f"[当前] 当前 IP: {current_ip}")
            return current_ip
        else:
            print(f"[错误] 无法获取 IP: HTTP {response.status_code}")
            return None
    except Exception as e:
        print(f"[错误] 获取 IP 失败: {e}")
        return None

def main():
    print("强制代理工具 - 确保使用新加坡 IP")
    print("=" * 50)
    
    # 测试当前 IP
    print("[检查] 检查当前 IP...")
    current_ip = test_current_ip()
    
    # 设置系统代理
    print("[配置] 设置系统代理...")
    if set_system_proxy(True, "127.0.0.1:8888"):
        print("[成功] 系统代理已设置")
    else:
        print("[警告] 系统代理设置失败，仅使用环境变量")
    
    # 设置环境变量
    proxy_url = "http://127.0.0.1:8888"
    env_vars = {
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url,
        'http_proxy': proxy_url,
        'https_proxy': proxy_url,
        'ALL_PROXY': proxy_url,
        'all_proxy': proxy_url
    }
    
    print("[环境] 设置环境变量...")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    print("-" * 50)
    print("[启动] 强制代理服务器: 127.0.0.1:8888")
    print("[转发] 目标: 127.0.0.1:7897 (新加坡)")
    print("[说明] 所有网络流量将强制通过新加坡代理")
    print("[提示] 按 Ctrl+C 停止并恢复设置")
    print("-" * 50)
    
    try:
        server = HTTPServer(('127.0.0.1', 8888), ForceProxyHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n[停止] 正在停止代理服务器...")
        server.shutdown()
        server.server_close()
        
        # 恢复系统代理设置
        print("[恢复] 恢复系统代理设置...")
        set_system_proxy(False)
        
        # 清理环境变量
        for var in env_vars.keys():
            if var in os.environ:
                del os.environ[var]
        
        print("[完成] 代理服务器已停止")
        print("[完成] 系统设置已恢复")
        
    except Exception as e:
        print(f"[错误] 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
