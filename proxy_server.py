#!/usr/bin/env python3
"""
Augment 代理服务器
将所有请求通过指定的新加坡 IP 地址发送
"""

import socket
import threading
import requests
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse, parse_qs
import ssl
import time

class ProxyHandler(BaseHTTPRequestHandler):
    # 目标代理 IP 地址（新加坡）
    PROXY_IP = "*************"
    PROXY_PORT = 7897  # Clash 代理端口
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")
    
    def do_GET(self):
        """处理 GET 请求"""
        self._handle_request()
    
    def do_POST(self):
        """处理 POST 请求"""
        self._handle_request()
    
    def do_PUT(self):
        """处理 PUT 请求"""
        self._handle_request()
    
    def do_DELETE(self):
        """处理 DELETE 请求"""
        self._handle_request()
    
    def do_PATCH(self):
        """处理 PATCH 请求"""
        self._handle_request()
    
    def do_OPTIONS(self):
        """处理 OPTIONS 请求"""
        self._handle_request()
    
    def _handle_request(self):
        """统一处理所有类型的请求"""
        try:
            # 获取请求信息
            url = self.path
            method = self.command
            headers = dict(self.headers)
            
            # 读取请求体
            content_length = int(headers.get('Content-Length', 0))
            body = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 移除可能导致问题的头部
            headers_to_remove = ['host', 'connection', 'content-length']
            for header in headers_to_remove:
                headers.pop(header, None)
            
            # 设置代理
            proxies = {
                'http': f'http://{self.PROXY_IP}:{self.PROXY_PORT}',
                'https': f'http://{self.PROXY_IP}:{self.PROXY_PORT}'
            }
            
            self.log_message(f"代理请求: {method} {url} 通过 {self.PROXY_IP}")
            
            # 发送请求
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body,
                proxies=proxies,
                timeout=30,
                verify=False,  # 忽略 SSL 证书验证
                allow_redirects=True
            )
            
            # 返回响应
            self.send_response(response.status_code)
            
            # 设置响应头
            for key, value in response.headers.items():
                if key.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(key, value)
            
            self.end_headers()
            
            # 发送响应体
            self.wfile.write(response.content)
            
        except requests.exceptions.ProxyError as e:
            self.log_message(f"代理错误: {e}")
            self._send_error_response(502, f"代理服务器错误: {e}")
        except requests.exceptions.Timeout as e:
            self.log_message(f"请求超时: {e}")
            self._send_error_response(504, "请求超时")
        except Exception as e:
            self.log_message(f"处理请求时出错: {e}")
            self._send_error_response(500, f"内部服务器错误: {e}")
    
    def _send_error_response(self, status_code, message):
        """发送错误响应"""
        try:
            self.send_response(status_code)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            
            error_response = {
                'error': message,
                'status': status_code,
                'proxy_ip': self.PROXY_IP
            }
            
            self.wfile.write(json.dumps(error_response).encode('utf-8'))
        except:
            pass

class ProxyServer:
    def __init__(self, host='127.0.0.1', port=8888):
        self.host = host
        self.port = port
        self.server = None
    
    def start(self):
        """启动代理服务器"""
        try:
            self.server = HTTPServer((self.host, self.port), ProxyHandler)
            print(f"🚀 Augment 代理服务器启动成功!")
            print(f"📍 监听地址: http://{self.host}:{self.port}")
            print(f"🌏 代理 IP: {ProxyHandler.PROXY_IP} (新加坡)")
            print(f"📝 配置说明:")
            print(f"   - 在 Augment 中设置 HTTP 代理为: {self.host}:{self.port}")
            print(f"   - 或设置环境变量: HTTP_PROXY=http://{self.host}:{self.port}")
            print(f"   - 或设置环境变量: HTTPS_PROXY=http://{self.host}:{self.port}")
            print(f"🔄 按 Ctrl+C 停止服务器")
            print("-" * 60)
            
            self.server.serve_forever()
            
        except KeyboardInterrupt:
            print("\n⏹️  正在停止代理服务器...")
            self.stop()
        except Exception as e:
            print(f"❌ 启动服务器失败: {e}")
    
    def stop(self):
        """停止代理服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("✅ 代理服务器已停止")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Augment 代理服务器')
    parser.add_argument('--host', default='127.0.0.1', help='监听主机地址 (默认: 127.0.0.1)')
    parser.add_argument('--port', type=int, default=8888, help='监听端口 (默认: 8888)')
    parser.add_argument('--proxy-port', type=int, default=7897, help='目标代理端口 (默认: 7897)')
    
    args = parser.parse_args()
    
    # 设置代理端口
    ProxyHandler.PROXY_PORT = args.proxy_port
    
    # 创建并启动服务器
    proxy_server = ProxyServer(args.host, args.port)
    proxy_server.start()

if __name__ == "__main__":
    main()
