# Augment 代理脚本

让 Augment 的所有请求都通过新加坡 IP 地址 (*************) 发送的代理脚本。

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 一键启动 (推荐)
```bash
python start_augment_proxy.py
```

这个脚本会自动：
- 启动代理服务器
- 设置环境变量
- 测试代理连接
- 显示使用说明

### 3. 手动启动

#### 步骤 1: 启动代理服务器
```bash
python proxy_server.py
```

#### 步骤 2: 设置环境变量

**Windows:**
```cmd
set_proxy.bat
```

**Linux/Mac:**
```bash
source ./set_proxy.sh
```

**或手动设置:**
```bash
export HTTP_PROXY=http://127.0.0.1:8888
export HTTPS_PROXY=http://127.0.0.1:8888
```

#### 步骤 3: 启动 Augment
在设置了环境变量的终端中启动 Augment。

## 📁 文件说明

- `proxy_server.py` - 主代理服务器
- `setup_proxy.py` - 环境配置脚本
- `start_augment_proxy.py` - 一键启动脚本
- `proxy_config.json` - 配置文件 (运行后生成)
- `set_proxy.bat` / `set_proxy.sh` - 环境变量设置脚本

## ⚙️ 配置选项

### 修改监听端口
```bash
python proxy_server.py --port 9999
```

### 修改目标代理端口
```bash
python proxy_server.py --proxy-port 7897
```

### 修改监听地址
```bash
python proxy_server.py --host 0.0.0.0 --port 8888
```

## 🧪 测试代理

### 测试代理连接
```bash
curl -x http://127.0.0.1:8888 http://httpbin.org/ip
```

### 测试 HTTPS
```bash
curl -x http://127.0.0.1:8888 https://httpbin.org/ip
```

## 🌏 代理信息

- **目标 IP:** *************
- **目标端口:** 7897 (Clash)
- **位置:** 新加坡
- **ISP:** Akari Networks
- **本地代理:** http://127.0.0.1:8888

## 🔧 故障排除

### 1. 端口被占用
```bash
# 查看端口占用
netstat -ano | findstr :8888  # Windows
lsof -i :8888                 # Linux/Mac

# 使用其他端口
python proxy_server.py --port 9999
```

### 2. 代理连接失败
- 检查目标代理服务器是否可用
- 确认防火墙设置
- 尝试不同的代理端口

### 3. 环境变量未生效
- 确保在设置环境变量的同一终端中启动 Augment
- 检查环境变量是否正确设置：`echo $HTTP_PROXY`

## 📝 日志

代理服务器会显示详细的请求日志，包括：
- 请求时间
- 请求方法和 URL
- 代理状态
- 错误信息

## 🛡️ 安全说明

- 此脚本仅用于开发和测试目的
- 请确保目标代理服务器的合法性
- 不要在生产环境中使用未经验证的代理

## 📞 支持

如果遇到问题，请检查：
1. 网络连接是否正常
2. 防火墙设置
3. 代理服务器可用性
4. 环境变量设置

---

**注意:** 使用代理服务器时请遵守相关法律法规和服务条款。
