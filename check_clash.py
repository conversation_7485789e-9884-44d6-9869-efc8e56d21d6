#!/usr/bin/env python3
"""
检查 Clash 配置和状态
"""

import requests
import json

def check_clash_api():
    """检查 Clash API"""
    try:
        # 常见的 Clash API 端口
        api_ports = [9090, 9091, 25500]
        
        for port in api_ports:
            try:
                url = f"http://127.0.0.1:{port}/proxies"
                response = requests.get(url, timeout=3)
                
                if response.status_code == 200:
                    print(f"[发现] Clash API: 127.0.0.1:{port}")
                    
                    data = response.json()
                    proxies = data.get('proxies', {})
                    
                    print(f"[代理] 可用代理节点:")
                    for name, proxy in proxies.items():
                        if proxy.get('type') != 'Selector':
                            proxy_type = proxy.get('type', 'Unknown')
                            print(f"  - {name}: {proxy_type}")
                    
                    # 检查当前选择的代理
                    selectors = {k: v for k, v in proxies.items() if v.get('type') == 'Selector'}
                    if selectors:
                        print(f"[选择] 当前代理选择:")
                        for name, selector in selectors.items():
                            current = selector.get('now', 'Unknown')
                            print(f"  - {name}: {current}")
                    
                    return port
                    
            except:
                continue
        
        print("[错误] 未找到 Clash API")
        return None
        
    except Exception as e:
        print(f"[错误] 检查 Clash API 失败: {e}")
        return None

def test_direct_clash():
    """直接测试 Clash 代理"""
    try:
        proxies = {
            'http': 'http://127.0.0.1:7897',
            'https': 'http://127.0.0.1:7897'
        }
        
        print("[测试] 直接测试 Clash 代理...")
        response = requests.get('http://httpbin.org/ip', proxies=proxies, timeout=10)
        
        if response.status_code == 200:
            ip_info = response.json()
            current_ip = ip_info.get('origin', 'Unknown')
            print(f"[结果] Clash 代理 IP: {current_ip}")
            
            # 判断是否为新加坡 IP
            if current_ip.startswith('188.253'):
                print("[成功] 正在使用新加坡代理!")
                return True
            else:
                print("[警告] 不是新加坡 IP，可能需要切换节点")
                return False
        else:
            print(f"[失败] HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[错误] 测试失败: {e}")
        return False

def switch_clash_proxy(api_port, selector_name, proxy_name):
    """切换 Clash 代理节点"""
    try:
        url = f"http://127.0.0.1:{api_port}/proxies/{selector_name}"
        data = {"name": proxy_name}
        
        response = requests.put(url, json=data, timeout=5)
        
        if response.status_code == 204:
            print(f"[成功] 已切换到: {proxy_name}")
            return True
        else:
            print(f"[失败] 切换失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[错误] 切换失败: {e}")
        return False

def main():
    print("Clash 配置检查工具")
    print("=" * 40)
    
    # 检查 Clash API
    api_port = check_clash_api()
    
    if not api_port:
        print("[建议] 请确保 Clash 正在运行并开启了 API")
        print("[建议] 检查 Clash 配置中的 external-controller 设置")
    
    print("-" * 40)
    
    # 直接测试 Clash 代理
    if test_direct_clash():
        print("[结论] Clash 代理工作正常，使用新加坡节点")
    else:
        print("[结论] Clash 代理可能需要调整")
        
        if api_port:
            print("[建议] 尝试手动切换到新加坡节点")
            print("[建议] 或者在 Clash 客户端中选择新加坡服务器")
    
    print("-" * 40)
    print("[说明] 如果 Clash 工作正常，问题可能是:")
    print("  1. 浏览器没有使用系统代理")
    print("  2. 应用程序绕过了代理设置")
    print("  3. 需要重启浏览器或应用")

if __name__ == "__main__":
    main()
