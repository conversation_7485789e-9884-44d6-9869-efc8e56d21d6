#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版 Augment 代理脚本 - Windows 优化版
"""

import os
import sys
import socket
import threading
import requests
import json
from http.server import HTTPServer, BaseHTTPRequestHandler
import time

# 设置控制台编码
if sys.platform == 'win32':
    import codecs
    sys.stdout = codecs.getwriter('utf-8')(sys.stdout.buffer, 'strict')
    sys.stderr = codecs.getwriter('utf-8')(sys.stderr.buffer, 'strict')

class SimpleProxyHandler(BaseHTTPRequestHandler):
    # Clash 代理配置
    PROXY_IP = "*************"
    PROXY_PORT = 7897
    
    def log_message(self, format, *args):
        """简化日志格式"""
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")
    
    def do_GET(self):
        self._handle_request()
    
    def do_POST(self):
        self._handle_request()
    
    def do_PUT(self):
        self._handle_request()
    
    def do_DELETE(self):
        self._handle_request()
    
    def do_PATCH(self):
        self._handle_request()
    
    def do_OPTIONS(self):
        self._handle_request()
    
    def _handle_request(self):
        """处理请求"""
        try:
            url = self.path
            method = self.command
            headers = dict(self.headers)
            
            # 读取请求体
            content_length = int(headers.get('Content-Length', 0))
            body = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 清理头部
            for header in ['host', 'connection', 'content-length']:
                headers.pop(header, None)
            
            # 设置代理
            proxies = {
                'http': f'http://{self.PROXY_IP}:{self.PROXY_PORT}',
                'https': f'http://{self.PROXY_IP}:{self.PROXY_PORT}'
            }
            
            # 发送请求
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body,
                proxies=proxies,
                timeout=30,
                verify=False,
                allow_redirects=True
            )
            
            # 返回响应
            self.send_response(response.status_code)
            
            for key, value in response.headers.items():
                if key.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(key, value)
            
            self.end_headers()
            self.wfile.write(response.content)
            
        except Exception as e:
            self.log_message(f"请求处理错误: {e}")
            self._send_error(500, str(e))
    
    def _send_error(self, status_code, message):
        """发送错误响应"""
        try:
            self.send_response(status_code)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(f"代理错误: {message}".encode('utf-8'))
        except:
            pass

def main():
    """主函数"""
    print("Augment 代理服务器 - 简化版")
    print("=" * 40)
    
    # 检查 requests 库
    try:
        import requests
    except ImportError:
        print("[错误] 缺少 requests 库")
        print("[安装] 请运行: pip install requests")
        input("按回车键退出...")
        return
    
    # 配置信息
    host = '127.0.0.1'
    port = 8888
    proxy_ip = SimpleProxyHandler.PROXY_IP
    proxy_port = SimpleProxyHandler.PROXY_PORT
    
    print(f"[配置] 本地监听: {host}:{port}")
    print(f"[配置] 目标代理: {proxy_ip}:{proxy_port} (新加坡)")
    print("-" * 40)
    
    # 设置环境变量
    proxy_url = f"http://{host}:{port}"
    env_vars = {
        'HTTP_PROXY': proxy_url,
        'HTTPS_PROXY': proxy_url,
        'http_proxy': proxy_url,
        'https_proxy': proxy_url,
    }
    
    print("[环境] 设置代理环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    print("-" * 40)
    
    try:
        # 启动服务器
        server = HTTPServer((host, port), SimpleProxyHandler)
        print(f"[启动] 代理服务器已启动")
        print(f"[说明] 现在可以启动 Augment，所有请求将通过新加坡代理")
        print(f"[提示] 按 Ctrl+C 停止服务器")
        print("-" * 40)
        
        # 运行服务器
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n[停止] 正在停止代理服务器...")
        server.shutdown()
        server.server_close()
        
        # 清理环境变量
        for var in env_vars.keys():
            if var in os.environ:
                del os.environ[var]
        
        print("[完成] 代理服务器已停止")
        print("[清理] 环境变量已清理")
        
    except Exception as e:
        print(f"[错误] 服务器启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
