#!/usr/bin/env python3
"""
测试版代理脚本
"""

import os
import sys
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
import time

class TestProxyHandler(BaseHTTPRequestHandler):
    PROXY_IP = "*************"
    PROXY_PORT = 7897
    
    def log_message(self, format, *args):
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")
    
    def do_GET(self):
        self._handle_request()
    
    def do_POST(self):
        self._handle_request()
    
    def _handle_request(self):
        try:
            url = self.path
            method = self.command
            headers = dict(self.headers)
            
            content_length = int(headers.get('Content-Length', 0))
            body = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 清理头部
            for header in ['host', 'connection', 'content-length']:
                headers.pop(header, None)
            
            # 设置代理
            proxies = {
                'http': f'http://{self.PROXY_IP}:{self.PROXY_PORT}',
                'https': f'http://{self.PROXY_IP}:{self.PROXY_PORT}'
            }
            
            print(f"[代理] {method} {url}")
            
            # 发送请求
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body,
                proxies=proxies,
                timeout=30,
                verify=False
            )
            
            # 返回响应
            self.send_response(response.status_code)
            
            for key, value in response.headers.items():
                if key.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(key, value)
            
            self.end_headers()
            self.wfile.write(response.content)
            
        except Exception as e:
            print(f"[错误] {e}")
            self.send_response(500)
            self.end_headers()
            self.wfile.write(f"Error: {e}".encode())

def main():
    print("测试代理服务器")
    print("目标: *************:7897")
    print("本地: 127.0.0.1:8888")
    print("-" * 30)
    
    # 设置环境变量
    proxy_url = "http://127.0.0.1:8888"
    os.environ['HTTP_PROXY'] = proxy_url
    os.environ['HTTPS_PROXY'] = proxy_url
    
    print("环境变量已设置")
    print("启动服务器...")
    
    try:
        server = HTTPServer(('127.0.0.1', 8888), TestProxyHandler)
        print("服务器已启动，按 Ctrl+C 停止")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n停止服务器...")
        server.shutdown()
        print("已停止")

if __name__ == "__main__":
    main()
