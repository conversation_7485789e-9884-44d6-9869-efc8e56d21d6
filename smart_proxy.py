#!/usr/bin/env python3
"""
智能代理脚本 - 自动检测和配置
"""

import os
import sys
import socket
import requests
from http.server import HTTPServer, BaseHTTPRequestHandler
import time

class SmartProxyHandler(BaseHTTPRequestHandler):
    # 默认配置
    PROXY_IP = "127.0.0.1"  # 先尝试本地 Clash
    PROXY_PORT = 7890       # Clash 默认端口
    
    def log_message(self, format, *args):
        timestamp = time.strftime('%H:%M:%S')
        print(f"[{timestamp}] {format % args}")
    
    def do_GET(self):
        self._handle_request()
    
    def do_POST(self):
        self._handle_request()
    
    def _handle_request(self):
        try:
            url = self.path
            method = self.command
            headers = dict(self.headers)
            
            content_length = int(headers.get('Content-Length', 0))
            body = self.rfile.read(content_length) if content_length > 0 else b''
            
            # 清理头部
            for header in ['host', 'connection', 'content-length']:
                headers.pop(header, None)
            
            # 设置代理
            proxies = {
                'http': f'http://{self.PROXY_IP}:{self.PROXY_PORT}',
                'https': f'http://{self.PROXY_IP}:{self.PROXY_PORT}'
            }
            
            # 发送请求
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body,
                proxies=proxies,
                timeout=30,
                verify=False
            )
            
            # 返回响应
            self.send_response(response.status_code)
            
            for key, value in response.headers.items():
                if key.lower() not in ['connection', 'transfer-encoding']:
                    self.send_header(key, value)
            
            self.end_headers()
            self.wfile.write(response.content)
            
        except Exception as e:
            print(f"[错误] {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'text/plain; charset=utf-8')
            self.end_headers()
            self.wfile.write(f"代理错误: {e}".encode('utf-8'))

def check_proxy_port(host, port):
    """检查代理端口是否可用"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(3)
        result = sock.connect_ex((host, port))
        sock.close()
        return result == 0
    except:
        return False

def find_clash_proxy():
    """查找可用的 Clash 代理"""
    # 常见的 Clash 端口
    common_ports = [7890, 7891, 7892, 7897, 8080, 8888, 1080]
    
    print("[检测] 正在查找 Clash 代理...")
    
    # 先检查本地
    for port in common_ports:
        if check_proxy_port('127.0.0.1', port):
            print(f"[发现] 本地 Clash 代理: 127.0.0.1:{port}")
            return '127.0.0.1', port
    
    # 再检查指定的新加坡 IP
    singapore_ip = '*************'
    for port in common_ports:
        if check_proxy_port(singapore_ip, port):
            print(f"[发现] 新加坡代理: {singapore_ip}:{port}")
            return singapore_ip, port
    
    print("[警告] 未找到可用的代理服务器")
    return None, None

def test_proxy_connection(proxy_host, proxy_port):
    """测试代理连接"""
    try:
        proxies = {
            'http': f'http://{proxy_host}:{proxy_port}',
            'https': f'http://{proxy_host}:{proxy_port}'
        }
        
        response = requests.get(
            'http://httpbin.org/ip',
            proxies=proxies,
            timeout=10
        )
        
        if response.status_code == 200:
            ip_info = response.json()
            current_ip = ip_info.get('origin', 'Unknown')
            print(f"[测试] 代理连接成功! 当前 IP: {current_ip}")
            return True
        else:
            print(f"[测试] 代理测试失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"[测试] 代理测试失败: {e}")
        return False

def main():
    print("智能代理配置工具")
    print("=" * 40)
    
    # 查找可用的代理
    proxy_host, proxy_port = find_clash_proxy()
    
    if not proxy_host:
        print("[错误] 未找到可用的代理服务器")
        print("[建议] 请确保 Clash 或其他代理软件正在运行")
        print("[建议] 常见端口: 7890, 7891, 7897, 8080")
        input("按回车键退出...")
        return
    
    # 设置代理配置
    SmartProxyHandler.PROXY_IP = proxy_host
    SmartProxyHandler.PROXY_PORT = proxy_port
    
    print(f"[配置] 使用代理: {proxy_host}:{proxy_port}")
    
    # 测试代理连接
    if not test_proxy_connection(proxy_host, proxy_port):
        print("[警告] 代理测试失败，但仍会启动服务器")
    
    # 设置环境变量
    local_proxy = "http://127.0.0.1:8888"
    env_vars = {
        'HTTP_PROXY': local_proxy,
        'HTTPS_PROXY': local_proxy,
        'http_proxy': local_proxy,
        'https_proxy': local_proxy,
    }
    
    print(f"[环境] 设置环境变量: {local_proxy}")
    for key, value in env_vars.items():
        os.environ[key] = value
    
    print("-" * 40)
    print(f"[启动] 本地代理服务器: 127.0.0.1:8888")
    print(f"[转发] 目标代理: {proxy_host}:{proxy_port}")
    print(f"[说明] 现在可以启动 Augment")
    print(f"[提示] 按 Ctrl+C 停止服务器")
    print("-" * 40)
    
    try:
        server = HTTPServer(('127.0.0.1', 8888), SmartProxyHandler)
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n[停止] 正在停止代理服务器...")
        server.shutdown()
        server.server_close()
        
        # 清理环境变量
        for var in env_vars.keys():
            if var in os.environ:
                del os.environ[var]
        
        print("[完成] 代理服务器已停止")
        print("[清理] 环境变量已清理")
        
    except Exception as e:
        print(f"[错误] 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
