#!/usr/bin/env python3
"""
设置 Augment 代理环境
自动配置系统代理设置
"""

import os
import sys
import subprocess
import platform
import json

class ProxySetup:
    def __init__(self):
        self.proxy_host = "127.0.0.1"
        self.proxy_port = "8888"
        self.proxy_url = f"http://{self.proxy_host}:{self.proxy_port}"
        self.target_ip = "*************"
    
    def set_environment_variables(self):
        """设置环境变量"""
        print("🔧 设置代理环境变量...")
        
        env_vars = {
            'HTTP_PROXY': self.proxy_url,
            'HTTPS_PROXY': self.proxy_url,
            'http_proxy': self.proxy_url,
            'https_proxy': self.proxy_url,
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
            print(f"   ✅ {key} = {value}")
        
        # 创建环境变量设置脚本
        self._create_env_script()
    
    def _create_env_script(self):
        """创建环境变量设置脚本"""
        system = platform.system().lower()
        
        if system == "windows":
            self._create_windows_script()
        else:
            self._create_unix_script()
    
    def _create_windows_script(self):
        """创建 Windows 批处理脚本"""
        script_content = f"""@echo off
echo 设置 Augment 代理环境变量...
set HTTP_PROXY={self.proxy_url}
set HTTPS_PROXY={self.proxy_url}
set http_proxy={self.proxy_url}
set https_proxy={self.proxy_url}

echo ✅ 代理环境变量已设置
echo 📍 代理地址: {self.proxy_url}
echo 🌏 目标 IP: {self.target_ip} (新加坡)
echo.
echo 使用方法:
echo 1. 运行此脚本设置环境变量
echo 2. 在同一命令行窗口中启动 Augment
echo.
pause
"""
        
        with open("set_proxy.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        
        print("📝 已创建 Windows 脚本: set_proxy.bat")
    
    def _create_unix_script(self):
        """创建 Unix/Linux/Mac 脚本"""
        script_content = f"""#!/bin/bash
echo "🔧 设置 Augment 代理环境变量..."
export HTTP_PROXY="{self.proxy_url}"
export HTTPS_PROXY="{self.proxy_url}"
export http_proxy="{self.proxy_url}"
export https_proxy="{self.proxy_url}"

echo "✅ 代理环境变量已设置"
echo "📍 代理地址: {self.proxy_url}"
echo "🌏 目标 IP: {self.target_ip} (新加坡)"
echo ""
echo "使用方法:"
echo "1. source ./set_proxy.sh"
echo "2. 在同一终端中启动 Augment"
echo ""
"""
        
        with open("set_proxy.sh", "w") as f:
            f.write(script_content)
        
        # 设置执行权限
        os.chmod("set_proxy.sh", 0o755)
        
        print("📝 已创建 Unix 脚本: set_proxy.sh")
    
    def create_config_file(self):
        """创建配置文件"""
        config = {
            "proxy": {
                "host": self.proxy_host,
                "port": int(self.proxy_port),
                "url": self.proxy_url,
                "target_ip": self.target_ip,
                "location": "Singapore"
            },
            "environment": {
                "HTTP_PROXY": self.proxy_url,
                "HTTPS_PROXY": self.proxy_url,
                "http_proxy": self.proxy_url,
                "https_proxy": self.proxy_url
            }
        }
        
        with open("proxy_config.json", "w", encoding="utf-8") as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        
        print("📝 已创建配置文件: proxy_config.json")
    
    def show_instructions(self):
        """显示使用说明"""
        print("\n" + "="*60)
        print("🚀 Augment 代理设置完成!")
        print("="*60)
        print(f"📍 本地代理地址: {self.proxy_url}")
        print(f"🌏 目标 IP: {self.target_ip} (新加坡)")
        print("\n📋 使用步骤:")
        print("1️⃣  启动代理服务器:")
        print("   python proxy_server.py")
        print("\n2️⃣  设置环境变量 (选择一种方式):")
        
        if platform.system().lower() == "windows":
            print("   方式A: 运行 set_proxy.bat")
            print("   方式B: 手动设置环境变量")
        else:
            print("   方式A: source ./set_proxy.sh")
            print("   方式B: 手动设置环境变量")
        
        print("\n3️⃣  启动 Augment (在设置了环境变量的终端中)")
        print("\n🔧 高级配置:")
        print("   - 修改代理端口: python proxy_server.py --port 9999")
        print("   - 修改目标代理端口: python proxy_server.py --proxy-port 3128")
        print("\n📊 测试代理:")
        print("   curl -x http://127.0.0.1:8888 http://httpbin.org/ip")
        print("="*60)

def main():
    """主函数"""
    print("🔧 正在设置 Augment 代理环境...")
    
    setup = ProxySetup()
    setup.set_environment_variables()
    setup.create_config_file()
    setup.show_instructions()

if __name__ == "__main__":
    main()
